'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import { ProductQueries, CustomerQueries } from '@nutripro/database'
import { Button } from '@nutripro/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@nutripro/ui'

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState({
    totalProducts: 0,
    lowStockProducts: 0,
    totalCustomers: 0,
    retailCustomers: 0,
    wholesaleCustomers: 0,
    loading: true
  })

  useEffect(() => {
    const loadStats = async () => {
      try {
        const productQueries = new ProductQueries()
        const customerQueries = new CustomerQueries()

        const [products, lowStock, customerStats] = await Promise.all([
          productQueries.getAll(),
          productQueries.getLowStock(10),
          customerQueries.getStats()
        ])

        setStats({
          totalProducts: products?.length || 0,
          lowStockProducts: lowStock?.length || 0,
          totalCustomers: customerStats.total,
          retailCustomers: customerStats.retail,
          wholesaleCustomers: customerStats.wholesale,
          loading: false
        })
      } catch (error) {
        console.error('Failed to load dashboard stats:', error)
        setStats(prev => ({ ...prev, loading: false }))
      }
    }

    loadStats()
  }, [])

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Welcome back, {user?.email || 'Demo User'}</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6 md:mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.loading ? '...' : stats.totalProducts}
              </div>
              <p className="text-xs text-blue-600">Active inventory items</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Total Customers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.loading ? '...' : stats.totalCustomers}
              </div>
              <p className="text-xs text-green-600">
                {stats.retailCustomers} retail, {stats.wholesaleCustomers} wholesale
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Retail Customers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats.loading ? '...' : stats.retailCustomers}
              </div>
              <p className="text-xs text-blue-600">Individual customers</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                Low Stock Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${stats.lowStockProducts > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.loading ? '...' : stats.lowStockProducts}
              </div>
              <p className={`text-xs ${stats.lowStockProducts > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {stats.lowStockProducts > 0 ? 'Requires attention' : 'All items well stocked'}
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>Latest customer orders</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Order #1234</p>
                    <p className="text-sm text-gray-600">John Doe</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">$89.99</p>
                    <p className="text-sm text-green-600">Completed</p>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Order #1235</p>
                    <p className="text-sm text-gray-600">Jane Smith</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">$156.50</p>
                    <p className="text-sm text-yellow-600">Processing</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" className="h-20 flex-col" onClick={() => window.open('/test-db', '_blank')}>
                  <span className="text-lg mb-1">🗄️</span>
                  <span>Test Database</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-lg mb-1">📦</span>
                  <span>Manage Products</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-lg mb-1">👥</span>
                  <span>View Customers</span>
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <span className="text-lg mb-1">🏃‍♂️</span>
                  <span>Coach Program</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
