"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _nutripro_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutripro/database */ \"(app-pages-browser)/../../packages/database/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Button!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Button!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Card,CardContent,CardDescription,CardHeader,CardTitle!=!../../packages/ui/src/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Input!=!@nutripro/ui */ \"(app-pages-browser)/__barrel_optimize__?names=Input!=!../../packages/ui/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProductsPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadProducts();\n    }, []);\n    const loadProducts = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_2__.ProductQueries();\n            const data = await productQueries.getAll();\n            setProducts(data || []);\n        } catch (err) {\n            console.error(\"Failed to load products:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to load products\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSearch = async ()=>{\n        if (!searchQuery.trim()) {\n            loadProducts();\n            return;\n        }\n        try {\n            setLoading(true);\n            setError(null);\n            const productQueries = new _nutripro_database__WEBPACK_IMPORTED_MODULE_2__.ProductQueries();\n            const data = await productQueries.search(searchQuery);\n            setProducts(data || []);\n        } catch (err) {\n            console.error(\"Search failed:\", err);\n            setError(err instanceof Error ? err.message : \"Search failed\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStockStatus = (product)=>{\n        if (product.stock_quantity <= 0) return {\n            status: \"Out of Stock\",\n            color: \"text-red-600\"\n        };\n        if (product.stock_quantity <= product.min_stock_level) return {\n            status: \"Low Stock\",\n            color: \"text-yellow-600\"\n        };\n        return {\n            status: \"In Stock\",\n            color: \"text-green-600\"\n        };\n    };\n    if (loading && products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 md:p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-64\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-4\",\n                            children: \"Loading products...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-4 md:p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 md:mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-bold text-gray-900\",\n                                        children: \"Products\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Manage your inventory and product catalog\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 sm:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"w-full sm:w-auto\",\n                                    children: \"Add Product\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: \"Search Products\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    children: \"Find products by name, SKU, or description\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Input_nutripro_ui__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"Search products...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            onKeyPress: (e)=>e.key === \"Enter\" && handleSearch()\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleSearch,\n                                                disabled: loading,\n                                                children: loading ? \"Searching...\" : \"Search\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"outline\",\n                                                onClick: ()=>{\n                                                    setSearchQuery(\"\");\n                                                    loadProducts();\n                                                },\n                                                children: \"Clear\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"mb-6 border-red-200 bg-red-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6\",\n                    children: products.map((product)=>{\n                        const stockStatus = getStockStatus(product);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"hover:shadow-lg transition-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-lg truncate\",\n                                                        children: product.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            \"SKU: \",\n                                                            product.sku\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium \".concat(stockStatus.color),\n                                                children: stockStatus.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 text-sm\",\n                                                children: [\n                                                    product.brands && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: product.brands.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    product.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: product.categories.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-semibold\",\n                                                            children: [\n                                                                \"AWG \",\n                                                                product.retail_price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        product.wholesale_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                \"Wholesale: AWG \",\n                                                                product.wholesale_price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Stock: \",\n                                                            product.stock_quantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-500\",\n                                                        children: [\n                                                            \"Min: \",\n                                                            product.min_stock_level\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 21\n                                            }, this),\n                                            product.product_variants && product.product_variants.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            \"Variants (\",\n                                                            product.product_variants.length,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            product.product_variants.slice(0, 3).map((variant)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded\",\n                                                                    children: variant.variant_value\n                                                                }, variant.id, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 29\n                                                                }, this)),\n                                                            product.product_variants.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    product.product_variants.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 pt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        className: \"flex-1\",\n                                                        children: \"View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                !loading && products.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"text-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"No products found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: searchQuery ? \"Try adjusting your search terms\" : \"Get started by adding your first product\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_nutripro_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                className: \"mt-4\",\n                                children: \"Add Product\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 11\n                }, this),\n                !loading && products.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardContent_CardDescription_CardHeader_CardTitle_nutripro_ui__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Total Products: \",\n                                        products.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Low Stock: \",\n                                        products.filter((p)=>p.stock_quantity <= p.min_stock_level).length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Out of Stock: \",\n                                        products.filter((p)=>p.stock_quantity <= 0).length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/NutriPro/apps/admin-panel/src/app/products/page.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsPage, \"7grl+TpvEbeO46Iu0yQwkFCpWDI=\");\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcHJvZHVjdHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUUyQztBQUVRO0FBQ2Q7QUFDbUQ7QUFDcEQ7QUF1QnJCLFNBQVNVOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1gsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dCLE9BQU9DLFNBQVMsR0FBR2pCLCtDQUFRQSxDQUFnQjtJQUVsREQsZ0RBQVNBLENBQUM7UUFDUm1CO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUEsZUFBZTtRQUNuQixJQUFJO1lBQ0ZMLFdBQVc7WUFDWEksU0FBUztZQUNULE1BQU1FLGlCQUFpQixJQUFJbEIsOERBQWNBO1lBQ3pDLE1BQU1tQixPQUFPLE1BQU1ELGVBQWVFLE1BQU07WUFDeENWLFlBQVlTLFFBQVEsRUFBRTtRQUN4QixFQUFFLE9BQU9FLEtBQUs7WUFDWkMsUUFBUVAsS0FBSyxDQUFDLDRCQUE0Qk07WUFDMUNMLFNBQVNLLGVBQWVFLFFBQVFGLElBQUlHLE9BQU8sR0FBRztRQUNoRCxTQUFVO1lBQ1JaLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWEsZUFBZTtRQUNuQixJQUFJLENBQUNaLFlBQVlhLElBQUksSUFBSTtZQUN2QlQ7WUFDQTtRQUNGO1FBRUEsSUFBSTtZQUNGTCxXQUFXO1lBQ1hJLFNBQVM7WUFDVCxNQUFNRSxpQkFBaUIsSUFBSWxCLDhEQUFjQTtZQUN6QyxNQUFNbUIsT0FBTyxNQUFNRCxlQUFlUyxNQUFNLENBQUNkO1lBQ3pDSCxZQUFZUyxRQUFRLEVBQUU7UUFDeEIsRUFBRSxPQUFPRSxLQUFLO1lBQ1pDLFFBQVFQLEtBQUssQ0FBQyxrQkFBa0JNO1lBQ2hDTCxTQUFTSyxlQUFlRSxRQUFRRixJQUFJRyxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSWixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1nQixpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsUUFBUUMsY0FBYyxJQUFJLEdBQUcsT0FBTztZQUFFQyxRQUFRO1lBQWdCQyxPQUFPO1FBQWU7UUFDeEYsSUFBSUgsUUFBUUMsY0FBYyxJQUFJRCxRQUFRSSxlQUFlLEVBQUUsT0FBTztZQUFFRixRQUFRO1lBQWFDLE9BQU87UUFBa0I7UUFDOUcsT0FBTztZQUFFRCxRQUFRO1lBQVlDLE9BQU87UUFBaUI7SUFDdkQ7SUFFQSxJQUFJckIsV0FBV0YsU0FBU3lCLE1BQU0sS0FBSyxHQUFHO1FBQ3BDLHFCQUNFLDhEQUFDQztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7Ozs7O3NDQUNmLDhEQUFDQzs0QkFBS0QsV0FBVTtzQ0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUtqQztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDRzt3Q0FBR0YsV0FBVTtrREFBK0M7Ozs7OztrREFDN0QsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7OzBDQUVwQyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNuQyw2RUFBTUE7b0NBQUNtQyxXQUFVOzhDQUFtQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRM0MsOERBQUNsQywwSEFBSUE7b0JBQUNrQyxXQUFVOztzQ0FDZCw4REFBQy9CLGdJQUFVQTs7OENBQ1QsOERBQUNDLCtIQUFTQTs4Q0FBQzs7Ozs7OzhDQUNYLDhEQUFDRixxSUFBZUE7OENBQUM7Ozs7Ozs7Ozs7OztzQ0FFbkIsOERBQUNELGlJQUFXQTtzQ0FDViw0RUFBQ2dDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUM3QiwyRUFBS0E7NENBQ0ppQyxhQUFZOzRDQUNaQyxPQUFPNUI7NENBQ1A2QixVQUFVLENBQUNDLElBQU03QixlQUFlNkIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUM5Q0ksWUFBWSxDQUFDRixJQUFNQSxFQUFFRyxHQUFHLEtBQUssV0FBV3JCOzs7Ozs7Ozs7OztrREFHNUMsOERBQUNVO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ25DLDZFQUFNQTtnREFBQzhDLFNBQVN0QjtnREFBY3VCLFVBQVVyQzswREFDdENBLFVBQVUsaUJBQWlCOzs7Ozs7MERBRTlCLDhEQUFDViw2RUFBTUE7Z0RBQ0xnRCxTQUFRO2dEQUNSRixTQUFTO29EQUNQakMsZUFBZTtvREFDZkc7Z0RBQ0Y7MERBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVNSRix1QkFDQyw4REFBQ2IsMEhBQUlBO29CQUFDa0MsV0FBVTs4QkFDZCw0RUFBQ2pDLGlJQUFXQTt3QkFBQ2lDLFdBQVU7a0NBQ3JCLDRFQUFDRzs0QkFBRUgsV0FBVTtzQ0FBZ0JyQjs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNbkMsOERBQUNvQjtvQkFBSUMsV0FBVTs4QkFDWjNCLFNBQVN5QyxHQUFHLENBQUMsQ0FBQ3JCO3dCQUNiLE1BQU1zQixjQUFjdkIsZUFBZUM7d0JBQ25DLHFCQUNFLDhEQUFDM0IsMEhBQUlBOzRCQUFrQmtDLFdBQVU7OzhDQUMvQiw4REFBQy9CLGdJQUFVQTtvQ0FBQytCLFdBQVU7OENBQ3BCLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzlCLCtIQUFTQTt3REFBQzhCLFdBQVU7a0VBQW9CUCxRQUFRdUIsSUFBSTs7Ozs7O2tFQUNyRCw4REFBQ2hELHFJQUFlQTt3REFBQ2dDLFdBQVU7OzREQUFVOzREQUM3QlAsUUFBUXdCLEdBQUc7Ozs7Ozs7Ozs7Ozs7MERBR3JCLDhEQUFDbEI7Z0RBQUlDLFdBQVcsdUJBQXlDLE9BQWxCZSxZQUFZbkIsS0FBSzswREFDckRtQixZQUFZcEIsTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSXpCLDhEQUFDNUIsaUlBQVdBOzhDQUNWLDRFQUFDZ0M7d0NBQUlDLFdBQVU7OzBEQUViLDhEQUFDRDtnREFBSUMsV0FBVTs7b0RBQ1pQLFFBQVF5QixNQUFNLGtCQUNiLDhEQUFDakI7d0RBQUtELFdBQVU7a0VBQ2JQLFFBQVF5QixNQUFNLENBQUNGLElBQUk7Ozs7OztvREFHdkJ2QixRQUFRMEIsVUFBVSxrQkFDakIsOERBQUNsQjt3REFBS0QsV0FBVTtrRUFDYlAsUUFBUTBCLFVBQVUsQ0FBQ0gsSUFBSTs7Ozs7Ozs7Ozs7OzBEQU05Qiw4REFBQ2pCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDs7c0VBQ0MsOERBQUNJOzREQUFFSCxXQUFVOztnRUFBd0I7Z0VBQUtQLFFBQVEyQixZQUFZOzs7Ozs7O3dEQUM3RDNCLFFBQVE0QixlQUFlLGtCQUN0Qiw4REFBQ2xCOzREQUFFSCxXQUFVOztnRUFBd0I7Z0VBQ25CUCxRQUFRNEIsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQU8vQyw4REFBQ3RCO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0M7OzREQUFLOzREQUFRUixRQUFRQyxjQUFjOzs7Ozs7O2tFQUNwQyw4REFBQ087d0RBQUtELFdBQVU7OzREQUFnQjs0REFBTVAsUUFBUUksZUFBZTs7Ozs7Ozs7Ozs7Ozs0Q0FJOURKLFFBQVE2QixnQkFBZ0IsSUFBSTdCLFFBQVE2QixnQkFBZ0IsQ0FBQ3hCLE1BQU0sR0FBRyxtQkFDN0QsOERBQUNDOztrRUFDQyw4REFBQ0k7d0RBQUVILFdBQVU7OzREQUF5Qzs0REFDekNQLFFBQVE2QixnQkFBZ0IsQ0FBQ3hCLE1BQU07NERBQUM7Ozs7Ozs7a0VBRTdDLDhEQUFDQzt3REFBSUMsV0FBVTs7NERBQ1pQLFFBQVE2QixnQkFBZ0IsQ0FBQ0MsS0FBSyxDQUFDLEdBQUcsR0FBR1QsR0FBRyxDQUFDLENBQUNELHdCQUN6Qyw4REFBQ1o7b0VBRUNELFdBQVU7OEVBRVRhLFFBQVFXLGFBQWE7bUVBSGpCWCxRQUFRWSxFQUFFOzs7Ozs0REFNbEJoQyxRQUFRNkIsZ0JBQWdCLENBQUN4QixNQUFNLEdBQUcsbUJBQ2pDLDhEQUFDRztnRUFBS0QsV0FBVTs7b0VBQXdCO29FQUNwQ1AsUUFBUTZCLGdCQUFnQixDQUFDeEIsTUFBTSxHQUFHO29FQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBEQVFoRCw4REFBQ0M7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDbkMsNkVBQU1BO3dEQUFDZ0QsU0FBUTt3REFBVWEsTUFBSzt3REFBSzFCLFdBQVU7a0VBQVM7Ozs7OztrRUFHdkQsOERBQUNuQyw2RUFBTUE7d0RBQUNnRCxTQUFRO3dEQUFVYSxNQUFLO3dEQUFLMUIsV0FBVTtrRUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJCQTdFcERQLFFBQVFnQyxFQUFFOzs7OztvQkFxRnpCOzs7Ozs7Z0JBSUQsQ0FBQ2xELFdBQVdGLFNBQVN5QixNQUFNLEtBQUssbUJBQy9CLDhEQUFDaEMsMEhBQUlBO29CQUFDa0MsV0FBVTs4QkFDZCw0RUFBQ2pDLGlJQUFXQTs7MENBQ1YsOERBQUNnQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFFSCxXQUFVO2tEQUEyQjs7Ozs7O2tEQUN4Qyw4REFBQ0c7d0NBQUVILFdBQVU7a0RBQ1Z2QixjQUFjLG9DQUFvQzs7Ozs7Ozs7Ozs7OzBDQUd2RCw4REFBQ1osNkVBQU1BO2dDQUFDbUMsV0FBVTswQ0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUTlCLENBQUN6QixXQUFXRixTQUFTeUIsTUFBTSxHQUFHLG1CQUM3Qiw4REFBQ2hDLDBIQUFJQTtvQkFBQ2tDLFdBQVU7OEJBQ2QsNEVBQUNqQyxpSUFBV0E7d0JBQUNpQyxXQUFVO2tDQUNyQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDQzs7d0NBQUs7d0NBQWlCNUIsU0FBU3lCLE1BQU07Ozs7Ozs7OENBQ3RDLDhEQUFDRzs7d0NBQUs7d0NBQ1E1QixTQUFTc0QsTUFBTSxDQUFDeEIsQ0FBQUEsSUFBS0EsRUFBRVQsY0FBYyxJQUFJUyxFQUFFTixlQUFlLEVBQUVDLE1BQU07Ozs7Ozs7OENBRWhGLDhEQUFDRzs7d0NBQUs7d0NBQ1c1QixTQUFTc0QsTUFBTSxDQUFDeEIsQ0FBQUEsSUFBS0EsRUFBRVQsY0FBYyxJQUFJLEdBQUdJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTbkY7R0E5UHdCMUI7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9wcm9kdWN0cy9wYWdlLnRzeD80ZTdkIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQgeyBQcm9kdWN0UXVlcmllcyB9IGZyb20gJ0BudXRyaXByby9kYXRhYmFzZSdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0BudXRyaXByby91aSdcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0BudXRyaXByby91aSdcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQG51dHJpcHJvL3VpJ1xuXG5pbnRlcmZhY2UgUHJvZHVjdCB7XG4gIGlkOiBzdHJpbmdcbiAgc2t1OiBzdHJpbmdcbiAgbmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsXG4gIHJldGFpbF9wcmljZTogbnVtYmVyXG4gIHdob2xlc2FsZV9wcmljZTogbnVtYmVyIHwgbnVsbFxuICBzdG9ja19xdWFudGl0eTogbnVtYmVyXG4gIG1pbl9zdG9ja19sZXZlbDogbnVtYmVyXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICBjYXRlZ29yaWVzPzogeyBpZDogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfSB8IG51bGxcbiAgYnJhbmRzPzogeyBpZDogc3RyaW5nOyBuYW1lOiBzdHJpbmcgfSB8IG51bGxcbiAgdmVuZG9ycz86IHsgaWQ6IHN0cmluZzsgbmFtZTogc3RyaW5nIH0gfCBudWxsXG4gIHByb2R1Y3RfdmFyaWFudHM/OiBBcnJheTx7XG4gICAgaWQ6IHN0cmluZ1xuICAgIHZhcmlhbnRfbmFtZTogc3RyaW5nXG4gICAgdmFyaWFudF92YWx1ZTogc3RyaW5nXG4gICAgc3RvY2tfcXVhbnRpdHk6IG51bWJlclxuICB9PlxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9kdWN0c1BhZ2UoKSB7XG4gIGNvbnN0IFtwcm9kdWN0cywgc2V0UHJvZHVjdHNdID0gdXNlU3RhdGU8UHJvZHVjdFtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW3NlYXJjaFF1ZXJ5LCBzZXRTZWFyY2hRdWVyeV0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZFByb2R1Y3RzKClcbiAgfSwgW10pXG5cbiAgY29uc3QgbG9hZFByb2R1Y3RzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBzZXRFcnJvcihudWxsKVxuICAgICAgY29uc3QgcHJvZHVjdFF1ZXJpZXMgPSBuZXcgUHJvZHVjdFF1ZXJpZXMoKVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHByb2R1Y3RRdWVyaWVzLmdldEFsbCgpXG4gICAgICBzZXRQcm9kdWN0cyhkYXRhIHx8IFtdKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGxvYWQgcHJvZHVjdHM6JywgZXJyKVxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBwcm9kdWN0cycpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2VhcmNoID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2VhcmNoUXVlcnkudHJpbSgpKSB7XG4gICAgICBsb2FkUHJvZHVjdHMoKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG4gICAgICBjb25zdCBwcm9kdWN0UXVlcmllcyA9IG5ldyBQcm9kdWN0UXVlcmllcygpXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcHJvZHVjdFF1ZXJpZXMuc2VhcmNoKHNlYXJjaFF1ZXJ5KVxuICAgICAgc2V0UHJvZHVjdHMoZGF0YSB8fCBbXSlcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1NlYXJjaCBmYWlsZWQ6JywgZXJyKVxuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdTZWFyY2ggZmFpbGVkJylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTdG9ja1N0YXR1cyA9IChwcm9kdWN0OiBQcm9kdWN0KSA9PiB7XG4gICAgaWYgKHByb2R1Y3Quc3RvY2tfcXVhbnRpdHkgPD0gMCkgcmV0dXJuIHsgc3RhdHVzOiAnT3V0IG9mIFN0b2NrJywgY29sb3I6ICd0ZXh0LXJlZC02MDAnIH1cbiAgICBpZiAocHJvZHVjdC5zdG9ja19xdWFudGl0eSA8PSBwcm9kdWN0Lm1pbl9zdG9ja19sZXZlbCkgcmV0dXJuIHsgc3RhdHVzOiAnTG93IFN0b2NrJywgY29sb3I6ICd0ZXh0LXllbGxvdy02MDAnIH1cbiAgICByZXR1cm4geyBzdGF0dXM6ICdJbiBTdG9jaycsIGNvbG9yOiAndGV4dC1ncmVlbi02MDAnIH1cbiAgfVxuXG4gIGlmIChsb2FkaW5nICYmIHByb2R1Y3RzLmxlbmd0aCA9PT0gMCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBtZDpwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0b1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtNFwiPkxvYWRpbmcgcHJvZHVjdHMuLi48L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBtZDpwLTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02IG1kOm1iLThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIG1kOnRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+UHJvZHVjdHM8L2gxPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5NYW5hZ2UgeW91ciBpbnZlbnRvcnkgYW5kIHByb2R1Y3QgY2F0YWxvZzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHNtOm10LTBcIj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBjbGFzc05hbWU9XCJ3LWZ1bGwgc206dy1hdXRvXCI+XG4gICAgICAgICAgICAgICAgQWRkIFByb2R1Y3RcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIFNlYXJjaCBhbmQgRmlsdGVycyAqL31cbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZT5TZWFyY2ggUHJvZHVjdHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+RmluZCBwcm9kdWN0cyBieSBuYW1lLCBTS1UsIG9yIGRlc2NyaXB0aW9uPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggcHJvZHVjdHMuLi5cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFF1ZXJ5fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hRdWVyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBvbktleVByZXNzPXsoZSkgPT4gZS5rZXkgPT09ICdFbnRlcicgJiYgaGFuZGxlU2VhcmNoKCl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17aGFuZGxlU2VhcmNofSBkaXNhYmxlZD17bG9hZGluZ30+XG4gICAgICAgICAgICAgICAgICB7bG9hZGluZyA/ICdTZWFyY2hpbmcuLi4nIDogJ1NlYXJjaCd9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiBcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCIgXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHNldFNlYXJjaFF1ZXJ5KCcnKVxuICAgICAgICAgICAgICAgICAgICBsb2FkUHJvZHVjdHMoKVxuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogRXJyb3IgU3RhdGUgKi99XG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWItNiBib3JkZXItcmVkLTIwMCBiZy1yZWQtNTBcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTcwMFwiPntlcnJvcn08L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogUHJvZHVjdHMgR3JpZCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC00IG1kOmdhcC02XCI+XG4gICAgICAgICAge3Byb2R1Y3RzLm1hcCgocHJvZHVjdCkgPT4ge1xuICAgICAgICAgICAgY29uc3Qgc3RvY2tTdGF0dXMgPSBnZXRTdG9ja1N0YXR1cyhwcm9kdWN0KVxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPENhcmQga2V5PXtwcm9kdWN0LmlkfSBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3dcIj5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtbGcgdHJ1bmNhdGVcIj57cHJvZHVjdC5uYW1lfTwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24gY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgU0tVOiB7cHJvZHVjdC5za3V9XG4gICAgICAgICAgICAgICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtzdG9ja1N0YXR1cy5jb2xvcn1gfT5cbiAgICAgICAgICAgICAgICAgICAgICB7c3RvY2tTdGF0dXMuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgICAgICB7LyogQnJhbmQgYW5kIENhdGVnb3J5ICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5icmFuZHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTgwMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5icmFuZHMubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmNhdGVnb3JpZXMgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYmctZ3JlZW4tMTAwIHRleHQtZ3JlZW4tODAwIHB4LTIgcHktMSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LmNhdGVnb3JpZXMubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogUHJpY2luZyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+QVdHIHtwcm9kdWN0LnJldGFpbF9wcmljZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC53aG9sZXNhbGVfcHJpY2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBXaG9sZXNhbGU6IEFXRyB7cHJvZHVjdC53aG9sZXNhbGVfcHJpY2V9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBTdG9jayBJbmZvICovfVxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+U3RvY2s6IHtwcm9kdWN0LnN0b2NrX3F1YW50aXR5fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+TWluOiB7cHJvZHVjdC5taW5fc3RvY2tfbGV2ZWx9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogVmFyaWFudHMgKi99XG4gICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LnByb2R1Y3RfdmFyaWFudHMgJiYgcHJvZHVjdC5wcm9kdWN0X3ZhcmlhbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWYXJpYW50cyAoe3Byb2R1Y3QucHJvZHVjdF92YXJpYW50cy5sZW5ndGh9KVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5wcm9kdWN0X3ZhcmlhbnRzLnNsaWNlKDAsIDMpLm1hcCgodmFyaWFudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3ZhcmlhbnQuaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcHgtMiBweS0xIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2YXJpYW50LnZhcmlhbnRfdmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QucHJvZHVjdF92YXJpYW50cy5sZW5ndGggPiAzICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICt7cHJvZHVjdC5wcm9kdWN0X3ZhcmlhbnRzLmxlbmd0aCAtIDN9IG1vcmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9ucyAqL31cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yIHB0LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBFZGl0XG4gICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgVmlld1xuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIClcbiAgICAgICAgICB9KX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEVtcHR5IFN0YXRlICovfVxuICAgICAgICB7IWxvYWRpbmcgJiYgcHJvZHVjdHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIG1iLTJcIj5ObyBwcm9kdWN0cyBmb3VuZDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICB7c2VhcmNoUXVlcnkgPyAnVHJ5IGFkanVzdGluZyB5b3VyIHNlYXJjaCB0ZXJtcycgOiAnR2V0IHN0YXJ0ZWQgYnkgYWRkaW5nIHlvdXIgZmlyc3QgcHJvZHVjdCd9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBjbGFzc05hbWU9XCJtdC00XCI+XG4gICAgICAgICAgICAgICAgQWRkIFByb2R1Y3RcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogU3VtbWFyeSAqL31cbiAgICAgICAgeyFsb2FkaW5nICYmIHByb2R1Y3RzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT1cIm10LTZcIj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXdyYXAgZ2FwLTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgPHNwYW4+VG90YWwgUHJvZHVjdHM6IHtwcm9kdWN0cy5sZW5ndGh9PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgICAgTG93IFN0b2NrOiB7cHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5zdG9ja19xdWFudGl0eSA8PSBwLm1pbl9zdG9ja19sZXZlbCkubGVuZ3RofVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgIE91dCBvZiBTdG9jazoge3Byb2R1Y3RzLmZpbHRlcihwID0+IHAuc3RvY2tfcXVhbnRpdHkgPD0gMCkubGVuZ3RofVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJQcm9kdWN0UXVlcmllcyIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJJbnB1dCIsIlByb2R1Y3RzUGFnZSIsInByb2R1Y3RzIiwic2V0UHJvZHVjdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJlcnJvciIsInNldEVycm9yIiwibG9hZFByb2R1Y3RzIiwicHJvZHVjdFF1ZXJpZXMiLCJkYXRhIiwiZ2V0QWxsIiwiZXJyIiwiY29uc29sZSIsIkVycm9yIiwibWVzc2FnZSIsImhhbmRsZVNlYXJjaCIsInRyaW0iLCJzZWFyY2giLCJnZXRTdG9ja1N0YXR1cyIsInByb2R1Y3QiLCJzdG9ja19xdWFudGl0eSIsInN0YXR1cyIsImNvbG9yIiwibWluX3N0b2NrX2xldmVsIiwibGVuZ3RoIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImgxIiwicCIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvbktleVByZXNzIiwia2V5Iiwib25DbGljayIsImRpc2FibGVkIiwidmFyaWFudCIsIm1hcCIsInN0b2NrU3RhdHVzIiwibmFtZSIsInNrdSIsImJyYW5kcyIsImNhdGVnb3JpZXMiLCJyZXRhaWxfcHJpY2UiLCJ3aG9sZXNhbGVfcHJpY2UiLCJwcm9kdWN0X3ZhcmlhbnRzIiwic2xpY2UiLCJ2YXJpYW50X3ZhbHVlIiwiaWQiLCJzaXplIiwiZmlsdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});