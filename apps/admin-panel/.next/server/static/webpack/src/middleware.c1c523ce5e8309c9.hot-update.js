"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/../../node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.50.3/node_modules/@supabase/ssr/dist/index.mjs\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(middleware)/../../node_modules/.pnpm/next@14.0.4_@babel+core@7.28.0_react-dom@18.3.1_react@18.3.1/node_modules/next/dist/esm/server/web/exports/next-response.js\");\n\n\nasync function middleware(request) {\n    let response = next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].next({\n        request: {\n            headers: request.headers\n        }\n    });\n    const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://placeholder.supabase.co\", \"placeholder_anon_key\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n                response = next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value,\n                    ...options\n                });\n            },\n            remove (name, options) {\n                response = next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].next({\n                    request: {\n                        headers: request.headers\n                    }\n                });\n                response.cookies.set({\n                    name,\n                    value: \"\",\n                    ...options\n                });\n            }\n        }\n    });\n    const { data: { user } } = await supabase.auth.getUser();\n    // If user is not signed in and trying to access protected routes\n    if (!user && !request.nextUrl.pathname.startsWith(\"/auth\")) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].redirect(new URL(\"/auth/login\", request.url));\n    }\n    // If user is signed in and trying to access auth pages\n    if (user && request.nextUrl.pathname.startsWith(\"/auth\")) {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].redirect(new URL(\"/dashboard\", request.url));\n    }\n    // If user is signed in and accessing root, redirect to dashboard\n    if (user && request.nextUrl.pathname === \"/\") {\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].redirect(new URL(\"/dashboard\", request.url));\n    }\n    return response;\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */ \"/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});