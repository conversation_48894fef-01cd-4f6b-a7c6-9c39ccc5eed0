{"name": "@nutripro/ui", "version": "0.1.0", "private": true, "description": "Shared UI components for NutriPro applications", "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src/", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.303.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}